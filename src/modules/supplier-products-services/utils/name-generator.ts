import { DynamicFieldSchema } from "../types";

/**
 * Generates a product/service name based on category and dynamic fields marked for product naming
 * @param categoryName - The name of the category
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @param queryService - Optional query service for hotel/destination name resolution
 * @returns Generated name string
 */
export async function generateProductServiceName(
  categoryName: string,
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = [],
  queryService?: any
): Promise<string> {
  console.log(`🏷️ Generating product/service name for category: ${categoryName}`);
  console.log(`📋 Custom fields:`, customFields);
  console.log(`🔧 Dynamic field schema:`, dynamicFieldSchema);
  console.log(`🔍 Query service available:`, !!queryService);

  const nameParts: string[] = [categoryName];

  // Get fields that should be used in product/service names (regardless of required status)
  const nameFields = dynamicFieldSchema.filter(
    (field) => field.used_in_product
  );

  console.log(`📝 Fields marked for product naming:`, nameFields.map(f => ({ key: f.key, type: f.type })));

  // Sort fields by order if available, otherwise by key for consistent ordering
  const sortedNameFields = nameFields.sort((a, b) => {
    // If both have order, sort by order
    if (a.order !== undefined && b.order !== undefined) {
      return a.order - b.order;
    }
    // If only one has order, prioritize it
    if (a.order !== undefined) return -1;
    if (b.order !== undefined) return 1;
    // Otherwise sort alphabetically by key
    return a.key.localeCompare(b.key);
  });

  console.log(`🔢 Sorted name fields:`, sortedNameFields.map(f => ({ key: f.key, type: f.type, order: f.order })));

  // Add values from fields marked for product naming
  for (const field of sortedNameFields) {
    const value = customFields[field.key];
    console.log(`🔍 Processing field ${field.key} (${field.type}) with value:`, value);

    if (value !== undefined && value !== null && value !== "") {
      // Handle different field types
      let displayValue: string;

      switch (field.type) {
        case "hotels":
          console.log(`🏨 Processing hotel field: ${field.key}`);
          displayValue = await resolveHotelNames(value, queryService);
          break;
        case "destinations":
          console.log(`🌍 Processing destination field: ${field.key}`);
          displayValue = await resolveDestinationNames(value, queryService);
          break;
        case "addons":
          console.log(`🎯 Processing addons field: ${field.key}`);
          displayValue = await resolveAddonNames(value, queryService);
          break;
        case "multi-select":
          displayValue = Array.isArray(value)
            ? value.join(", ")
            : String(value);
          break;
        case "number-range":
          if (
            typeof value === "object" &&
            value.min !== undefined &&
            value.max !== undefined
          ) {
            displayValue = `${value.min}-${value.max}`;
          } else {
            displayValue = String(value);
          }
          break;
        case "boolean":
          displayValue = value ? "Yes" : "No";
          break;
        case "date":
          if (value instanceof Date) {
            displayValue = value.toLocaleDateString();
          } else {
            displayValue = String(value);
          }
          break;
        default:
          console.log(`📝 Processing default field type: ${field.type}`);
          displayValue = String(value);
      }

      console.log(`✨ Display value for ${field.key}: "${displayValue}"`);

      if (displayValue.trim()) {
        nameParts.push(displayValue.trim());
        console.log(`➕ Added to name parts: "${displayValue.trim()}"`);
      } else {
        console.log(`⚠️ Skipping empty display value for ${field.key}`);
      }
    } else {
      console.log(`⏭️ Skipping field ${field.key} - no value provided`);
    }
  }

  const finalName = nameParts.join(" – ");
  console.log(`🎯 Final generated name: "${finalName}"`);
  return finalName;
}

/**
 * Resolves hotel IDs to hotel names
 * @param value - Hotel ID(s) as string, array, or JSON string
 * @param queryService - Query service for resolving hotel names
 * @returns Comma-separated hotel names or original value if resolution fails
 */
async function resolveHotelNames(
  value: any,
  queryService?: any
): Promise<string> {
  if (!queryService) {
    console.warn("No query service available for hotel name resolution, falling back to IDs");
    return Array.isArray(value) ? value.join(", ") : String(value);
  }

  try {
    // Parse hotel IDs from various formats
    let hotelIds: string[] = [];
    if (Array.isArray(value)) {
      hotelIds = value;
    } else if (typeof value === "string") {
      try {
        const parsed = JSON.parse(value);
        hotelIds = Array.isArray(parsed) ? parsed : [parsed];
      } catch {
        hotelIds = [value];
      }
    } else {
      hotelIds = [String(value)];
    }

    console.log(`🏨 Resolving hotel names for IDs:`, hotelIds);

    // Use the provided query service to resolve hotel names
    const hotelNames: string[] = [];

    for (const hotelId of hotelIds) {
      try {
        console.log(`🔍 Querying hotel with ID: ${hotelId}`);
        const result = await queryService.graph({
          entity: "hotel",
          filters: { id: hotelId },
          fields: ["id", "name"],
        });

        console.log(`📊 Hotel query result for ${hotelId}:`, result);

        if (result.data && result.data.length > 0) {
          const hotelName = result.data[0].name;
          console.log(`✅ Resolved hotel ${hotelId} to name: ${hotelName}`);
          hotelNames.push(hotelName);
        } else {
          console.warn(`❌ No hotel found with ID ${hotelId}, using ID as fallback`);
          // Fallback to ID if hotel not found
          hotelNames.push(hotelId);
        }
      } catch (error) {
        console.warn(`❌ Failed to resolve hotel name for ID ${hotelId}:`, error);
        hotelNames.push(hotelId);
      }
    }

    const result = hotelNames.join(", ");
    console.log(`🏨 Final hotel names result: ${result}`);
    return result;
  } catch (error) {
    console.warn("❌ Error resolving hotel names:", error);
    return Array.isArray(value) ? value.join(", ") : String(value);
  }
}

/**
 * Resolves destination IDs to destination names
 * @param value - Destination ID(s) as string, array, or JSON string
 * @param queryService - Query service for resolving destination names
 * @returns Comma-separated destination names or original value if resolution fails
 */
async function resolveDestinationNames(
  value: any,
  queryService?: any
): Promise<string> {
  if (!queryService) {
    console.warn("No query service available for destination name resolution, falling back to IDs");
    return Array.isArray(value) ? value.join(", ") : String(value);
  }

  try {
    // Parse destination IDs from various formats
    let destinationIds: string[] = [];
    if (Array.isArray(value)) {
      destinationIds = value;
    } else if (typeof value === "string") {
      try {
        const parsed = JSON.parse(value);
        destinationIds = Array.isArray(parsed) ? parsed : [parsed];
      } catch {
        destinationIds = [value];
      }
    } else {
      destinationIds = [String(value)];
    }

    console.log(`🌍 Resolving destination names for IDs:`, destinationIds);

    // Use the provided query service to resolve destination names
    const destinationNames: string[] = [];

    for (const destinationId of destinationIds) {
      try {
        console.log(`🔍 Querying destination with ID: ${destinationId}`);
        const result = await queryService.graph({
          entity: "destination",
          filters: { id: destinationId },
          fields: ["id", "name"],
        });

        console.log(`📊 Destination query result for ${destinationId}:`, result);

        if (result.data && result.data.length > 0) {
          const destinationName = result.data[0].name;
          console.log(`✅ Resolved destination ${destinationId} to name: ${destinationName}`);
          destinationNames.push(destinationName);
        } else {
          console.warn(`❌ No destination found with ID ${destinationId}, using ID as fallback`);
          // Fallback to ID if destination not found
          destinationNames.push(destinationId);
        }
      } catch (error) {
        console.warn(
          `❌ Failed to resolve destination name for ID ${destinationId}:`,
          error
        );
        destinationNames.push(destinationId);
      }
    }

    const result = destinationNames.join(", ");
    console.log(`🌍 Final destination names result: ${result}`);
    return result;
  } catch (error) {
    console.warn("❌ Error resolving destination names:", error);
    return Array.isArray(value) ? value.join(", ") : String(value);
  }
}

/**
 * Resolves addon/product service IDs to their names
 * @param value - Product service ID(s) as string, array, or JSON string
 * @param queryService - Query service for resolving product service names
 * @returns Comma-separated product service names or original value if resolution fails
 */
async function resolveAddonNames(
  value: any,
  queryService?: any
): Promise<string> {
  if (!queryService) {
    console.warn("No query service available for addon name resolution, falling back to IDs");
    return Array.isArray(value) ? value.join(", ") : String(value);
  }

  try {
    // Parse addon IDs from various formats
    let addonIds: string[] = [];
    if (Array.isArray(value)) {
      addonIds = value;
    } else if (typeof value === "string") {
      try {
        const parsed = JSON.parse(value);
        addonIds = Array.isArray(parsed) ? parsed : [parsed];
      } catch {
        addonIds = [value];
      }
    } else {
      addonIds = [String(value)];
    }

    console.log(`🎯 Resolving addon names for IDs:`, addonIds);

    // Use the provided query service to resolve addon names
    const addonNames: string[] = [];

    for (const addonId of addonIds) {
      try {
        console.log(`🔍 Querying product service with ID: ${addonId}`);

        // Query as product_service since addons field type refers to product services
        const result = await queryService.graph({
          entity: "product_service",
          filters: { id: addonId },
          fields: ["id", "name"],
        });

        console.log(`📊 Product service query result for ${addonId}:`, result);

        if (result.data && result.data.length > 0) {
          const addonName = result.data[0].name;
          console.log(`✅ Resolved addon ${addonId} to name: ${addonName}`);
          addonNames.push(addonName);
        } else {
          console.warn(`❌ No product service found with ID ${addonId}, using ID as fallback`);
          // Fallback to ID if addon not found
          addonNames.push(addonId);
        }
      } catch (error) {
        console.warn(`❌ Failed to resolve addon name for ID ${addonId}:`, error);
        addonNames.push(addonId);
      }
    }

    const result = addonNames.join(", ");
    console.log(`🎯 Final addon names result: ${result}`);
    return result;
  } catch (error) {
    console.warn("❌ Error resolving addon names:", error);
    return Array.isArray(value) ? value.join(", ") : String(value);
  }
}

/**
 * Generates a unique key for product/service uniqueness validation
 * @param categoryId - The category ID
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @returns Unique key string for validation
 */
export function generateProductServiceUniqueKey(
  categoryId: string,
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = []
): string {
  const keyParts: string[] = [categoryId];

  // Get fields that should be used in uniqueness validation (same as product naming)
  const uniqueFields = dynamicFieldSchema.filter(
    (field) => field.used_in_product
  );

  // Sort fields by order if available, otherwise by key for consistent ordering
  const sortedFields = uniqueFields.sort((a, b) => {
    // If both have order, sort by order
    if (a.order !== undefined && b.order !== undefined) {
      return a.order - b.order;
    }
    // If only one has order, prioritize it
    if (a.order !== undefined) return -1;
    if (b.order !== undefined) return 1;
    // Otherwise sort alphabetically by key
    return a.key.localeCompare(b.key);
  });

  // Add values from fields marked for product naming
  for (const field of sortedFields) {
    const value = customFields[field.key];
    let keyValue: string;

    if (value === undefined || value === null || value === "") {
      keyValue = "__EMPTY__";
    } else {
      // Normalize values for consistent comparison
      switch (field.type) {
        case "multi-select":
          keyValue = Array.isArray(value)
            ? value.sort().join("|")
            : String(value);
          break;
        case "number-range":
          if (
            typeof value === "object" &&
            value.min !== undefined &&
            value.max !== undefined
          ) {
            keyValue = `${value.min}:${value.max}`;
          } else {
            keyValue = String(value);
          }
          break;
        case "boolean":
          keyValue = value ? "true" : "false";
          break;
        case "date":
          if (value instanceof Date) {
            keyValue = value.toISOString().split("T")[0]; // YYYY-MM-DD format
          } else {
            keyValue = String(value);
          }
          break;
        default:
          keyValue = String(value).toLowerCase().trim();
      }

      keyParts.push(`${field.key}:${keyValue}`);
    }
  }

  return keyParts.join("||");
}

/**
 * Generates a unique key for supplier offering uniqueness validation
 * @param supplierId - The supplier ID
 * @param productServiceId - The product/service ID
 * @param activeFrom - The active from date
 * @param activeTo - The active to date
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @returns Unique key string for validation
 */
export function generateSupplierOfferingUniqueKey(
  supplierId: string,
  productServiceId: string,
  activeFrom?: Date | string,
  activeTo?: Date | string,
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = []
): string {
  const keyParts: string[] = [supplierId, productServiceId];

  // Add date range to uniqueness key
  const fromDate = activeFrom
    ? activeFrom instanceof Date
      ? activeFrom.toISOString().split("T")[0]
      : activeFrom
    : "__NO_FROM__";
  const toDate = activeTo
    ? activeTo instanceof Date
      ? activeTo.toISOString().split("T")[0]
      : activeTo
    : "__NO_TO__";
  keyParts.push(`${fromDate}:${toDate}`);

  // Get mandatory fields that should be used in supplier offering uniqueness
  const mandatoryFields = dynamicFieldSchema.filter(
    (field) => field.required && field.used_in_supplier_offering
  );

  // Sort fields by key to ensure consistent ordering
  const sortedFields = mandatoryFields.sort((a, b) =>
    a.key.localeCompare(b.key)
  );

  // Add values from mandatory fields
  for (const field of sortedFields) {
    const value = customFields[field.key];
    let keyValue: string;

    if (value === undefined || value === null || value === "") {
      keyValue = "__EMPTY__";
    } else {
      // Normalize values for consistent comparison
      switch (field.type) {
        case "multi-select":
          keyValue = Array.isArray(value)
            ? value.sort().join("|")
            : String(value);
          break;
        case "number-range":
          if (
            typeof value === "object" &&
            value.min !== undefined &&
            value.max !== undefined
          ) {
            keyValue = `${value.min}:${value.max}`;
          } else {
            keyValue = String(value);
          }
          break;
        case "boolean":
          keyValue = value ? "true" : "false";
          break;
        case "date":
          if (value instanceof Date) {
            keyValue = value.toISOString().split("T")[0]; // YYYY-MM-DD format
          } else {
            keyValue = String(value);
          }
          break;
        default:
          keyValue = String(value).toLowerCase().trim();
      }

      keyParts.push(`${field.key}:${keyValue}`);
    }
  }

  return keyParts.join("||");
}
