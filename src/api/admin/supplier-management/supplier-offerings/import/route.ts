import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import { PostAdminImportSupplierOfferings } from "../validators";
import {
  parseDateAsUTCStartOfDay,
  parseDateAsUTCEndOfDay,
} from "src/utils/date-utils";

// Utility function to parse various date formats from Excel
const parseExcelDate = (dateValue: any): Date | null => {
  if (!dateValue || dateValue === "") return null;

  try {
    // If it's already a Date object
    if (dateValue instanceof Date) {
      return dateValue;
    }

    // If it's a number (Excel serial date)
    if (typeof dateValue === "number") {
      // Excel serial date to JavaScript date
      const excelEpoch = new Date(1900, 0, 1);
      return new Date(
        excelEpoch.getTime() + (dateValue - 2) * 24 * 60 * 60 * 1000
      );
    }

    // If it's a string, try various formats
    if (typeof dateValue === "string") {
      const trimmedValue = dateValue.trim();

      // Handle Excel date objects that have been converted to strings
      if (trimmedValue.includes("GMT") || trimmedValue.includes("Time")) {
        const date = new Date(trimmedValue);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // Try common date formats using UTC-safe parsing
      // ISO format (YYYY-MM-DD)
      const isoMatch = trimmedValue.match(/^(\d{4})-(\d{1,2})-(\d{1,2})$/);
      if (isoMatch) {
        const [, year, month, day] = isoMatch;
        // Use UTC constructor to avoid timezone issues - months are 0-based
        const date = new Date(
          Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day))
        );
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // European format (DD/MM/YYYY)
      const euroMatch = trimmedValue.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
      if (euroMatch) {
        const [, day, month, year] = euroMatch;
        // Use UTC constructor to avoid timezone issues - months are 0-based
        const date = new Date(
          Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day))
        );
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // Dot format (DD.MM.YYYY)
      const dotMatch = trimmedValue.match(/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/);
      if (dotMatch) {
        const [, day, month, year] = dotMatch;
        // Use UTC constructor to avoid timezone issues - months are 0-based
        const date = new Date(
          Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day))
        );
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // Fallback: try native Date parsing
      const fallbackDate = new Date(trimmedValue);
      if (!isNaN(fallbackDate.getTime())) {
        return fallbackDate;
      }
    }

    return null;
  } catch (error) {
    console.error("Error parsing date:", dateValue, error);
    return null;
  }
};

type PostAdminImportSupplierOfferingsType = z.infer<
  typeof PostAdminImportSupplierOfferings
>;

/**
 * POST /admin/supplier-management/supplier-offerings/import
 * Bulk import supplier offerings from CSV/Excel data
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminImportSupplierOfferingsType>,
  res: MedusaResponse
) => {
  try {
    // Handle both validated and raw body for debugging
    const requestBody = req.validatedBody || req.body;
    const { supplier_offerings } = requestBody;

    // Debug: Log received data
    console.log(
      "🔍 Backend Import Debug - Received supplier_offerings count:",
      supplier_offerings?.length
    );
    console.log(
      "🔍 Backend Import Debug - First offering data:",
      supplier_offerings?.[0]
    );

    if (
      !supplier_offerings ||
      !Array.isArray(supplier_offerings) ||
      supplier_offerings.length === 0
    ) {
      return res.status(400).json({
        type: "validation_error",
        message: "Supplier offerings array is required and must not be empty",
      });
    }

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Validate each supplier offering data
    const validationErrors: Array<{
      index: number;
      field: string;
      message: string;
      value?: any;
    }> = [];

    // Process each supplier offering
    const results = {
      created: 0,
      errors: [] as Array<{ index: number; message: string; data: any }>,
    };

    for (let i = 0; i < supplier_offerings.length; i++) {
      const offeringData = supplier_offerings[i];

      try {
        // Parse dates using timezone-safe UTC parsing
        let activeFromDate = null;
        let activeToDate = null;

        if (offeringData.active_from) {
          activeFromDate = parseDateAsUTCStartOfDay(offeringData.active_from);
        }

        if (offeringData.active_to) {
          activeToDate = parseDateAsUTCEndOfDay(offeringData.active_to);
        }

        const processedData: any = {
          ...offeringData,
          active_from: activeFromDate,
          active_to: activeToDate,
          created_by: req.auth_context?.actor_id,
        };

        // Debug: Log processed data before sending to service
        console.log(
          `🔍 Backend Import Debug - Processing item ${i + 1}:`,
          processedData
        );
        console.log(`🔍 Backend Import Debug - Pricing fields being sent:`, {
          gross_price: processedData.gross_price,
          commission: processedData.commission,
          net_cost: processedData.net_cost,
          margin_rate: processedData.margin_rate,
          selling_price: processedData.selling_price,
          selling_currency: processedData.selling_currency,
          exchange_rate: processedData.exchange_rate,
          selling_price_selling_currency:
            processedData.selling_price_selling_currency,
        });

        // Create the supplier offering
        try {
          const result =
            await supplierProductsServicesService.createSupplierOffering(
              processedData
            );
          console.log(
            `🔍 Backend Import Debug - Successfully created offering:`,
            result.id
          );
        } catch (serviceError) {
          console.error(
            `🔍 Backend Import Debug - Service error for item ${i + 1}:`,
            serviceError
          );
          throw serviceError;
        }
        results.created++;
      } catch (error) {
        console.error(`❌ Failed to create supplier offering ${i + 1}:`, error);

        // Extract meaningful error message
        let errorMessage = "Unknown error occurred";
        if (error instanceof Error) {
          errorMessage = error.message;
        } else if (typeof error === "string") {
          errorMessage = error;
        }

        // Log the specific error for debugging
        if (errorMessage.includes("Date range conflict")) {
          console.error(`Date range conflict for row ${i + 1}:`, errorMessage);
        }

        results.errors.push({
          index: i + 1,
          message: errorMessage,
          data: offeringData,
        });
      }
    }

    // Prepare response
    const response = {
      success: results.errors.length === 0,
      message:
        results.errors.length === 0
          ? `Successfully imported ${results.created} supplier offerings`
          : `Imported ${results.created} supplier offerings with ${results.errors.length} errors`,
      created: results.created,
      errors: results.errors,
    };

    // Log specific error types for debugging
    if (results.errors.length > 0) {
      const dateConflictErrors = results.errors.filter(
        (err) =>
          err.message.includes("Date range conflict") ||
          err.message.includes("Validity period overlaps")
      );
    }

    if (results.errors.length > 0) {
      return res.status(207).json(response); // 207 Multi-Status for partial success
    }

    return res.status(201).json(response);
  } catch (error) {
    console.error("💥 Import supplier offerings error:", error);

    return res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error
          ? error.message
          : "Internal server error occurred during import",
    });
  }
};
