import { z } from "zod";
import { parseDateAsUTC } from "src/utils/date-utils";

// Custom price validation schema
const customPriceSchema = z.object({
  name: z.string().min(1, "Custom price name is required"),
  price: z.number().min(0, "Custom price must be non-negative"),
});

// Create Supplier Offering Validation
export const PostAdminCreateSupplierOffering = z
  .object({
    product_service_id: z.string().min(1, "Product/Service ID is required"),
    supplier_id: z.string().min(1, "Supplier ID is required"),
    active_from: z.string().optional().nullable(),
    active_to: z.string().optional().nullable(),
    availability_notes: z.string().optional().nullable(),

    // Legacy cost field (for backward compatibility and duplicate functionality)
    cost: z.number().min(0, "Cost must be non-negative").optional().nullable(),

    // Enhanced pricing fields
    commission: z
      .number()
      .min(0)
      .max(1, "Commission must be between 0 and 1 (0% to 100%)")
      .optional(),
    gross_price: z
      .number()
      .min(0, "Gross price must be non-negative")
      .optional(),
    net_cost: z.number().min(0, "Net cost must be non-negative").optional(),
    net_price: z.number().min(0, "Net price must be non-negative").optional(),
    margin_rate: z
      .number()
      .min(0)
      .max(0.9999, "Margin rate must be between 0 and 0.9999 (0% to 99.99%)")
      .optional(),
    selling_price: z
      .number()
      .min(0, "Selling price must be non-negative")
      .optional(),
    custom_prices: z.array(customPriceSchema).optional(),

    // Currency fields (support null for duplicate functionality)
    currency: z
      .string()
      .regex(/^[A-Z]{3}$/, "Currency must be a valid 3-letter code")
      .optional()
      .nullable(),
    currency_override: z.boolean().optional().default(false),

    // Selling currency fields
    selling_currency: z
      .string()
      .regex(/^[A-Z]{3}$/, "Selling currency must be a valid 3-letter code")
      .optional(),
    selling_price_selling_currency: z
      .union([z.string(), z.number()])
      .transform((val) => {
        if (typeof val === "string") {
          const parsed = parseFloat(val);
          return isNaN(parsed) ? undefined : parsed;
        }
        return val;
      })
      .refine((val) => val === undefined || val >= 0, {
        message: "Selling price in selling currency must be non-negative",
      })
      .optional(),
    exchange_rate: z
      .union([z.string(), z.number()])
      .transform((val) => {
        if (typeof val === "string") {
          const parsed = parseFloat(val);
          return isNaN(parsed) ? undefined : parsed;
        }
        return val;
      })
      .refine((val) => val === undefined || val > 0, {
        message: "Exchange rate must be positive",
      })
      .optional(),
    exchange_rate_date: z
      .union([z.string(), z.date()])
      .transform((val) => {
        if (typeof val === "string") {
          const parsed = parseDateAsUTC(val);
          return parsed || undefined;
        }
        return val;
      })
      .optional(),

    status: z.enum(["active", "inactive"]).default("active"),
    custom_fields: z.record(z.any()).optional(),

    // Add-ons Configuration (JSON storage for addon line items with mandatory/optional settings)
    add_ons: z.array(z.any()).optional(), // JSON storage for addon line items with pricing and mandatory flags

    created_by: z.string().optional(),
  })
  .refine(
    (data) => {
      // If commission is provided, gross_price must also be provided
      if (data.commission && !data.gross_price) {
        return false;
      }
      return true;
    },
    {
      message: "Gross price is required when commission is provided",
      path: ["gross_price"],
    }
  )
  .refine(
    (data) => {
      // If margin_rate is provided for selling price calculation, we need net_price or the ability to calculate it
      if (
        data.margin_rate &&
        !data.net_price &&
        !data.net_cost &&
        !(data.commission && data.gross_price)
      ) {
        return false;
      }
      return true;
    },
    {
      message:
        "Net price (or supplier price, or commission + public price) is required when margin rate is provided",
      path: ["net_price"],
    }
  );

// Update Supplier Offering Validation
export const PostAdminUpdateSupplierOffering = z
  .object({
    product_service_id: z.string().min(1).optional(),
    supplier_id: z.string().min(1).optional(),
    active_from: z.string().optional().nullable(),
    active_to: z.string().optional().nullable(),
    availability_notes: z.string().optional().nullable(),

    // Legacy cost field (for backward compatibility and duplicate functionality)
    cost: z.number().min(0, "Cost must be non-negative").optional().nullable(),

    // Enhanced pricing fields
    commission: z
      .number()
      .min(0)
      .max(1, "Commission must be between 0 and 1 (0% to 100%)")
      .optional(),
    gross_price: z
      .number()
      .min(0, "Gross price must be non-negative")
      .optional(),
    net_cost: z.number().min(0, "Net cost must be non-negative").optional(),
    net_price: z.number().min(0, "Net price must be non-negative").optional(),
    margin_rate: z
      .number()
      .min(0)
      .max(0.9999, "Margin rate must be between 0 and 0.9999 (0% to 99.99%)")
      .optional(),
    selling_price: z
      .number()
      .min(0, "Selling price must be non-negative")
      .optional(),
    custom_prices: z.array(customPriceSchema).optional(),

    // Currency fields (support null for duplicate functionality)
    currency: z
      .string()
      .regex(/^[A-Z]{3}$/, "Currency must be a valid 3-letter code")
      .optional()
      .nullable(),
    currency_override: z.boolean().optional(),

    // Selling currency fields
    selling_currency: z
      .string()
      .regex(/^[A-Z]{3}$/, "Selling currency must be a valid 3-letter code")
      .optional(),
    selling_price_selling_currency: z
      .union([z.string(), z.number()])
      .transform((val) => {
        if (typeof val === "string") {
          const parsed = parseFloat(val);
          return isNaN(parsed) ? undefined : parsed;
        }
        return val;
      })
      .refine((val) => val === undefined || val >= 0, {
        message: "Selling price in selling currency must be non-negative",
      })
      .optional(),
    exchange_rate: z
      .union([z.string(), z.number()])
      .transform((val) => {
        if (typeof val === "string") {
          const parsed = parseFloat(val);
          return isNaN(parsed) ? undefined : parsed;
        }
        return val;
      })
      .refine((val) => val === undefined || val > 0, {
        message: "Exchange rate must be positive",
      })
      .optional(),
    exchange_rate_date: z
      .union([z.string(), z.date()])
      .transform((val) => {
        if (typeof val === "string") {
          const parsed = parseDateAsUTC(val);
          return parsed || undefined;
        }
        return val;
      })
      .optional(),

    status: z.enum(["active", "inactive"]).optional(),
    custom_fields: z.record(z.any()).optional(),

    // Add-ons Configuration (JSON storage for addon line items with mandatory/optional settings)
    add_ons: z.array(z.any()).optional(), // JSON storage for addon line items with pricing and mandatory flags

    updated_by: z.string().optional(),
    change_reason: z.string().optional(),
  })
  .refine(
    (data) => {
      // If commission is provided, gross_price must also be provided
      if (data.commission && !data.gross_price) {
        return false;
      }
      return true;
    },
    {
      message: "Gross price is required when commission is provided",
      path: ["gross_price"],
    }
  );

// List Supplier Offerings Query Validation
export const GetAdminSupplierOfferingsQuery = z.object({
  limit: z.string().optional(),
  offset: z.string().optional(),
  supplier_id: z.string().optional(),
  product_service_id: z.string().optional(),
  category_id: z.string().optional(),
  status: z.enum(["active", "inactive"]).optional(),
  active_from: z.string().optional(),
  active_to: z.string().optional(),
  created_by: z.string().optional(),
  updated_by: z.string().optional(),
  search: z.string().optional(),
  sort_by: z
    .enum(["product_service_name", "net_cost", "validity", "updated_at"])
    .optional()
    .default("updated_at"),
  sort_order: z.enum(["asc", "desc"]).optional().default("desc"),
});

// Delete Supplier Offering Validation
export const PostAdminDeleteSupplierOffering = z.object({
  id: z.string().min(1, "Supplier offering ID is required"),
});

// Import Supplier Offerings Validation
export const PostAdminImportSupplierOfferings = z.object({
  supplier_offerings: z
    .array(
      z
        .object({
          // Allow both ID and name fields for flexibility
          supplier_id: z.string().optional(),
          supplier_name: z.string().optional(),
          product_service_id: z.string().optional(),
          product_service_name: z.string().optional(),
          active_from: z.string().optional(),
          active_to: z.string().optional(),
          availability_notes: z.string().optional(),
          // Allow cost as string or number to handle Excel formatting (legacy field)
          cost: z
            .union([
              z.number().min(0, "Cost must be non-negative"),
              z.string().transform((val) => {
                if (val === "" || val === null || val === undefined)
                  return undefined;
                const parsed = parseFloat(val);
                if (isNaN(parsed))
                  throw new Error("Cost must be a valid number");
                if (parsed < 0) throw new Error("Cost must be non-negative");
                return parsed;
              }),
            ])
            .optional(),

          // Enhanced Pricing Fields (same as create/update validators)
          commission: z
            .union([
              z.number().min(0).max(1, "Commission must be between 0 and 1"),
              z.string().transform((val) => {
                if (val === "" || val === null || val === undefined)
                  return undefined;
                const parsed = parseFloat(val);
                if (isNaN(parsed))
                  throw new Error("Commission must be a valid number");
                if (parsed < 0 || parsed > 1)
                  throw new Error("Commission must be between 0 and 1");
                return parsed;
              }),
            ])
            .optional(),
          gross_price: z
            .union([
              z.number().min(0, "Gross price must be non-negative"),
              z.string().transform((val) => {
                if (val === "" || val === null || val === undefined)
                  return undefined;
                const parsed = parseFloat(val);
                if (isNaN(parsed))
                  throw new Error("Gross price must be a valid number");
                if (parsed < 0)
                  throw new Error("Gross price must be non-negative");
                return parsed;
              }),
            ])
            .optional(),
          net_cost: z
            .union([
              z.number().min(0, "Net cost must be non-negative"),
              z.string().transform((val) => {
                if (val === "" || val === null || val === undefined)
                  return undefined;
                const parsed = parseFloat(val);
                if (isNaN(parsed))
                  throw new Error("Net cost must be a valid number");
                if (parsed < 0)
                  throw new Error("Net cost must be non-negative");
                return parsed;
              }),
            ])
            .optional(),
          margin_rate: z
            .union([
              z
                .number()
                .min(0)
                .max(0.9999, "Margin rate must be between 0 and 0.9999"),
              z.string().transform((val) => {
                if (val === "" || val === null || val === undefined)
                  return undefined;
                const parsed = parseFloat(val);
                if (isNaN(parsed))
                  throw new Error("Margin rate must be a valid number");
                if (parsed < 0 || parsed > 0.9999)
                  throw new Error("Margin rate must be between 0 and 0.9999");
                return parsed;
              }),
            ])
            .optional(),
          selling_price: z
            .union([
              z.number().min(0, "Selling price must be non-negative"),
              z.string().transform((val) => {
                if (val === "" || val === null || val === undefined)
                  return undefined;
                const parsed = parseFloat(val);
                if (isNaN(parsed))
                  throw new Error("Selling price must be a valid number");
                if (parsed < 0)
                  throw new Error("Selling price must be non-negative");
                return parsed;
              }),
            ])
            .optional(),

          // Currency fields
          currency: z
            .string()
            .regex(/^[A-Z]{3}$/, "Currency must be a valid 3-letter code")
            .optional(),
          selling_currency: z
            .string()
            .regex(
              /^[A-Z]{3}$/,
              "Selling currency must be a valid 3-letter code"
            )
            .optional(),
          exchange_rate: z
            .union([
              z.number().min(0, "Exchange rate must be positive"),
              z.string().transform((val) => {
                if (val === "" || val === null || val === undefined)
                  return undefined;
                const parsed = parseFloat(val);
                if (isNaN(parsed))
                  throw new Error("Exchange rate must be a valid number");
                if (parsed <= 0)
                  throw new Error("Exchange rate must be positive");
                return parsed;
              }),
            ])
            .optional(),
          selling_price_selling_currency: z
            .union([
              z
                .number()
                .min(
                  0,
                  "Selling price in selling currency must be non-negative"
                ),
              z.string().transform((val) => {
                if (val === "" || val === null || val === undefined)
                  return undefined;
                const parsed = parseFloat(val);
                if (isNaN(parsed))
                  throw new Error(
                    "Selling price in selling currency must be a valid number"
                  );
                if (parsed < 0)
                  throw new Error(
                    "Selling price in selling currency must be non-negative"
                  );
                return parsed;
              }),
            ])
            .optional(),
          currency_override: z
            .union([
              z.boolean(),
              z.string().transform((val) => {
                if (val === "" || val === null || val === undefined)
                  return false;
                const lower = val.toLowerCase();
                return ["true", "1", "yes"].includes(lower);
              }),
            ])
            .optional(),
          status: z.enum(["active", "inactive"]).default("active"),
          custom_fields: z.record(z.any()).optional(),
        })
        .refine(
          (data) => {
            // At least one supplier identifier is required
            return data.supplier_id || data.supplier_name;
          },
          {
            message: "Either supplier_id or supplier_name is required",
          }
        )
        .refine(
          (data) => {
            // At least one product/service identifier is required
            return data.product_service_id || data.product_service_name;
          },
          {
            message:
              "Either product_service_id or product_service_name is required",
          }
        )
    )
    .min(1, "At least one supplier offering is required"),
});
