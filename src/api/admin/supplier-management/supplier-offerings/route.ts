import {
  MedusaRequest,
  MedusaResponse,
  AuthenticatedMedusaRequest,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import {
  PostAdminCreateSupplierOffering,
  GetAdminSupplierOfferingsQuery,
} from "./validators";
import {
  parseDateAsUTCStartOfDay,
  parseDateAsUTCEndOfDay,
} from "src/utils/date-utils";

type PostAdminCreateSupplierOfferingType = z.infer<
  typeof PostAdminCreateSupplierOffering
>;
type GetAdminSupplierOfferingsQueryType = z.infer<
  typeof GetAdminSupplierOfferingsQuery
>;

export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateSupplierOfferingType>,
  res: MedusaResponse
) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Validate request body
    const validatedData = PostAdminCreateSupplierOffering.parse(req.body);

    // Parse dates using timezone-safe UTC parsing
    let activeFromDate = null;
    let activeToDate = null;

    if (validatedData.active_from && validatedData.active_from !== null) {
      activeFromDate = parseDateAsUTCStartOfDay(validatedData.active_from);
    }

    if (validatedData.active_to && validatedData.active_to !== null) {
      activeToDate = parseDateAsUTCEndOfDay(validatedData.active_to);
    }

    const processedData: any = {
      ...validatedData,
      active_from: activeFromDate,
      active_to: activeToDate,
      created_by: req.auth_context?.actor_id || validatedData.created_by,
    };

    const supplierOffering =
      await supplierProductsServicesService.createSupplierOffering(
        processedData
      );

    // Emit supplier-offering.created event for price sync
    try {
      const eventBusService = req.scope.resolve(Modules.EVENT_BUS);
      await eventBusService.emit({
        name: "supplier-offering.created",
        data: {
          id: supplierOffering.id,
          ...supplierOffering,
        },
      });
    } catch (eventError) {
      console.error(
        "❌ Failed to emit supplier-offering.created event:",
        eventError
      );
      // Don't fail the request if event emission fails
    }

    res.status(201).json({
      supplier_offering: supplierOffering,
    });
  } catch (error) {
    console.error("Error creating supplier offering:", error);

    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid request data",
        details: error.errors,
      });
    }

    if (error.type === "duplicate_error") {
      return res.status(409).json({
        type: "duplicate_error",
        message: error.message,
      });
    }

    // Handle MedusaError types
    if (error.__isMedusaError && error.type === "duplicate_error") {
      return res.status(409).json({
        type: "duplicate_error",
        message: error.message,
      });
    }

    if (error.type === "not_found") {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: "Internal server error",
    });
  }
};

/**
 * Resolves hotel and destination IDs to names in custom_fields for supplier offerings
 */
async function resolveCustomFieldNames(
  offering: any,
  scope: any
): Promise<any> {
  if (
    !offering.custom_fields ||
    !offering.product_service?.category?.dynamic_field_schema
  ) {
    return offering;
  }

  const query = scope.resolve("query");
  const resolvedCustomFields = { ...offering.custom_fields };

  // Find hotel, destination, and addon fields in the category schema
  const hotelFields = offering.product_service.category.dynamic_field_schema.filter(
    (field: any) => field.type === "hotels"
  );
  const destinationFields = offering.product_service.category.dynamic_field_schema.filter(
    (field: any) => field.type === "destinations"
  );
  const addonFields = offering.product_service.category.dynamic_field_schema.filter(
    (field: any) => field.type === "addons"
  );

  // Resolve hotel field names
  for (const field of hotelFields) {
    if (resolvedCustomFields[field.key]) {
      try {
        let hotelIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(resolvedCustomFields[field.key])) {
          hotelIds = resolvedCustomFields[field.key];
        } else if (typeof resolvedCustomFields[field.key] === "string") {
          try {
            const parsed = JSON.parse(resolvedCustomFields[field.key]);
            hotelIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            hotelIds = [resolvedCustomFields[field.key]];
          }
        }

        const hotelNames: string[] = [];
        for (const hotelId of hotelIds) {
          try {
            const result = await query.graph({
              entity: "hotel",
              filters: { id: hotelId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              hotelNames.push(result.data[0].name);
            } else {
              hotelNames.push(hotelId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve hotel name for ID ${hotelId}:`,
              error
            );
            hotelNames.push(hotelId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = hotelIds;
        resolvedCustomFields[`${field.key}_names`] = hotelNames;
      } catch (error) {
        console.warn(
          `Error resolving hotel names for field ${field.key}:`,
          error
        );
      }
    }
  }

  // Resolve destination field names
  for (const field of destinationFields) {
    if (resolvedCustomFields[field.key]) {
      try {
        let destinationIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(resolvedCustomFields[field.key])) {
          destinationIds = resolvedCustomFields[field.key];
        } else if (typeof resolvedCustomFields[field.key] === "string") {
          try {
            const parsed = JSON.parse(resolvedCustomFields[field.key]);
            destinationIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            destinationIds = [resolvedCustomFields[field.key]];
          }
        }

        const destinationNames: string[] = [];
        for (const destinationId of destinationIds) {
          try {
            const result = await query.graph({
              entity: "destination",
              filters: { id: destinationId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              destinationNames.push(result.data[0].name);
            } else {
              destinationNames.push(destinationId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve destination name for ID ${destinationId}:`,
              error
            );
            destinationNames.push(destinationId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = destinationIds;
        resolvedCustomFields[`${field.key}_names`] = destinationNames;
      } catch (error) {
        console.warn(
          `Error resolving destination names for field ${field.key}:`,
          error
        );
      }
    }
  }

  // Resolve addon field names
  for (const field of addonFields) {
    if (resolvedCustomFields[field.key]) {
      try {
        let addonIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(resolvedCustomFields[field.key])) {
          addonIds = resolvedCustomFields[field.key];
        } else if (typeof resolvedCustomFields[field.key] === 'string') {
          try {
            const parsed = JSON.parse(resolvedCustomFields[field.key]);
            addonIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            addonIds = [resolvedCustomFields[field.key]];
          }
        }

        const addonNames: string[] = [];
        for (const addonId of addonIds) {
          try {
            const result = await query.graph({
              entity: "product_service",
              filters: { id: addonId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              addonNames.push(result.data[0].name);
            } else {
              addonNames.push(addonId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve addon name for ID ${addonId}:`,
              error
            );
            addonNames.push(addonId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = addonIds;
        resolvedCustomFields[`${field.key}_names`] = addonNames;
      } catch (error) {
        console.warn(
          `Error resolving addon names for field ${field.key}:`,
          error
        );
      }
    }
  }

  return {
    ...offering,
    custom_fields: resolvedCustomFields,
  };
}

export const GET = async (
  req: MedusaRequest<{}, GetAdminSupplierOfferingsQueryType>,
  res: MedusaResponse
) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Parse query parameters
    const limit = parseInt(req.query.limit as string) || 25;
    const offset = parseInt(req.query.offset as string) || 0;

    // Build filters
    const filters: any = {};

    if (req.query.supplier_id) {
      filters.supplier_id = req.query.supplier_id;
    }

    if (req.query.product_service_id) {
      filters.product_service_id = req.query.product_service_id;
    }

    if (req.query.category_id) {
      filters.category_id = req.query.category_id;
    }

    if (req.query.status) {
      filters.status = req.query.status;
    }

    if (req.query.active_from) {
      filters.active_from = new Date(req.query.active_from as string);
    }

    if (req.query.active_to) {
      filters.active_to = new Date(req.query.active_to as string);
    }

    if (req.query.created_by) {
      filters.created_by = req.query.created_by;
    }

    if (req.query.updated_by) {
      filters.updated_by = req.query.updated_by;
    }

    // Add search parameter support
    if (req.query.search) {
      filters.search = req.query.search;
    }

    // Add sorting parameters
    const sort_by = req.query.sort_by || "updated_at";
    const sort_order = req.query.sort_order || "desc";

    const result =
      await supplierProductsServicesService.listSupplierOfferingsWithFilters(
        filters,
        { limit, offset, sort_by, sort_order }
      );

    // Resolve custom field names for all offerings
    const resolvedOfferings = await Promise.all(
      result.data.map(async (offering: any) => {
        try {
          // Resolve custom field names for the offering
          const resolvedOffering = await resolveCustomFieldNames(
            offering,
            req.scope
          );

          // Also resolve product service name if it contains IDs
          if (resolvedOffering.product_service) {
            const resolvedProductService = await resolveCustomFieldNames(
              resolvedOffering.product_service,
              req.scope
            );
            resolvedOffering.product_service = resolvedProductService;
          }

          return resolvedOffering;
        } catch (error) {
          console.warn(
            `Failed to resolve names for offering ${offering.id}:`,
            error
          );
          return offering; // Return original offering if name resolution fails
        }
      })
    );

    // Debug: Log the first offering to see what data is being returned
    if (resolvedOfferings.length > 0) {
      console.log("🔍 API Debug - First offering data (with resolved names):", {
        id: resolvedOfferings[0].id,
        product_service_name: resolvedOfferings[0].product_service?.name,
        product_service_custom_fields:
          resolvedOfferings[0].product_service?.custom_fields,
        offering_custom_fields: resolvedOfferings[0].custom_fields,
      });
      console.log("🔍 API Debug - Product service name resolution:", {
        original_name: result.data[0]?.product_service?.name,
        resolved_name: resolvedOfferings[0].product_service?.name,
        has_custom_fields:
          !!resolvedOfferings[0].product_service?.custom_fields,
        has_category_schema:
          !!resolvedOfferings[0].product_service?.category
            ?.dynamic_field_schema,
      });
    }

    res.status(200).json({
      supplier_offerings: resolvedOfferings,
      count: result.count,
      limit: result.limit,
      offset: result.offset,
    });
  } catch (error) {
    console.error("Error listing supplier offerings:", error);

    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid query parameters",
        details: error.errors,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: "Internal server error",
    });
  }
};
