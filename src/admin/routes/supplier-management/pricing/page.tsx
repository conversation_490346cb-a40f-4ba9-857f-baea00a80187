import { useNavigate } from "react-router-dom";
import { useState, useEffect, ComponentType } from "react";
import { Container, Heading, Text, Button, Toaster } from "@camped-ai/ui";
import { Building2, CircleDollarSignIcon, Tags } from "lucide-react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import HotelPricingManager from "../../../components/hotel/pricing/hotel-pricing-manager-new";
import HotelSelector from "../../../components/hotel/hotel-selector";
import { useRbac } from "../../../hooks/use-rbac";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { useHotels } from "../../../hooks/supplier-products-services/use-hotels";

const CircleDollarSignIconComponent: ComponentType = (props: any) => (
  <CircleDollarSignIcon {...props} className="h-[15px] w-4" />
);

// Interfaces removed - HotelPricingManager will handle data fetching

const HotelPricingPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [selectedHotelId, setSelectedHotelId] = useState<string | null>(null);

  // Get hotels list for default selection
  const { data: hotelsData, isLoading: isLoadingHotels } = useHotels({
    limit: 100,
    is_active: true,
  });

  // Set default hotel selection when hotels are loaded
  useEffect(() => {
    if (
      hotelsData?.hotels &&
      hotelsData.hotels.length > 0 &&
      !selectedHotelId
    ) {
      const firstHotel = hotelsData.hotels[0];
      setSelectedHotelId(firstHotel.id);
    }
  }, [hotelsData, selectedHotelId]);

  // No need to fetch pricing data here - HotelPricingManager will handle it

  // Handle hotel selection change
  const handleHotelChange = (hotelId: string) => {
    setSelectedHotelId(hotelId);
  };

  // Check permissions first
  if (!hasPermission("pricing:view")) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            You don't have permission to view pricing information
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  // Show loading state while hotels are being fetched
  if (isLoadingHotels) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show error state if no hotels available
  if (!hotelsData?.hotels || hotelsData.hotels.length === 0) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">No hotels found</Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  const canEdit = hasPermission("pricing:edit");
  const canCreate = hasPermission("pricing:create");
  const canDelete = hasPermission("pricing:delete");

  // Determine if user should have read-only access
  const isReadOnly = !canEdit && !canCreate && !canDelete;

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <Container className="py-6">
        {/* Header with hotel selector */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <Heading level="h1" className="text-2xl font-bold">
                Hotel Pricing Management
              </Heading>
            </div>
          </div>

          {/* Hotel Selector */}
          <div className="max-w-md">
            <HotelSelector
              selectedHotelId={selectedHotelId}
              onHotelChange={handleHotelChange}
              label="Select Hotel"
              placeholder="Choose a hotel to manage pricing..."
              showActiveOnly={true}
            />
          </div>
        </div>

        {/* Pricing Manager */}
        {selectedHotelId && (
          <HotelPricingManager
            hotelId={selectedHotelId}
            onBack={() => navigate("/hotel-management/hotels")}
            canEdit={canEdit}
            canCreate={canCreate}
            canDelete={canDelete}
            hideBackButton={true}
          />
        )}

        {/* No Hotel Selected State */}
        {!selectedHotelId && (
          <div className="text-center py-12 bg-muted rounded-lg">
            <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <Text className="text-muted-foreground mb-4">
              Please select a hotel to view and manage pricing
            </Text>
          </div>
        )}
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Hotel Pricing",
  icon: CircleDollarSignIconComponent,
});

export default HotelPricingPage;
