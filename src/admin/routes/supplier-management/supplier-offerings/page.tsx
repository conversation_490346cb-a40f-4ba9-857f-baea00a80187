import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { PlusMini, MagnifyingGlass } from "@camped-ai/icons";
import {
  Package,
  Filter,
  Download,
  Upload,
  FileSpreadsheet,
  Edit,
  Eye,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUpDown,
  Trash,
  X,
  Co<PERSON>,
  History,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
  Select,
  DropdownMenu,
  FocusModal,
  Checkbox,
  DatePicker,
  Prompt,
  Popover,
  Switch,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import {
  useSupplierOfferings,
  useDeleteSupplierOffering,
  useDuplicateSupplierOffering,
  useUpdateSupplierOffering,
  useSupplierOfferingImportExport,
} from "../../../hooks/supplier-products-services/use-supplier-offerings";
import SupplierOfferingImportModal from "../../../components/supplier-management/supplier-offering-import-modal";
import SupplierOfferingExportModal from "../../../components/supplier-management/supplier-offering-export-modal";
import InlineEditCell from "../../../components/supplier-management/inline-edit-cell";

import PopoverEditValidityCell from "../../../components/supplier-management/popover-edit-validity-cell";
import PopoverEditStatusCell from "../../../components/supplier-management/popover-edit-status-cell";
import { useCategories } from "../../../hooks/supplier-products-services/use-categories";
import { useSuppliers } from "../../../hooks/vendor-management/use-suppliers";
import { useProductsServices } from "../../../hooks/supplier-products-services/use-products-services";
import { useHotels } from "../../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../../hooks/supplier-products-services/use-destinations";
// Constants

type SortField =
  | "product_service_name"
  | "net_cost"
  | "validity"
  | "updated_at";
type SortOrder = "asc" | "desc";

interface FilterState {
  supplier_id: string;
  category_id: string;
  product_service_id: string;
  status: "active" | "inactive" | "";
  active_from: Date | null;
  active_to: Date | null;
}

// Function to resolve hotel and destination IDs in product service names
const resolveProductServiceName = (productService: any, hotels: any[], destinations: any[]): string => {
  if (!productService?.name || !productService?.category?.dynamic_field_schema || !productService?.custom_fields) {
    return productService?.name || 'Unknown Product/Service';
  }

  let resolvedName = productService.name;

  // Find hotel and destination fields in the category schema
  const hotelFields = productService.category.dynamic_field_schema.filter(
    (field: any) => field.type === "hotels" && field.used_in_product !== false
  );
  const destinationFields = productService.category.dynamic_field_schema.filter(
    (field: any) => field.type === "destinations" && field.used_in_product !== false
  );

  // Resolve hotel field names
  hotelFields.forEach((field: any) => {
    if (productService.custom_fields[field.key]) {
      try {
        let hotelIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(productService.custom_fields[field.key])) {
          hotelIds = productService.custom_fields[field.key];
        } else if (typeof productService.custom_fields[field.key] === 'string') {
          try {
            const parsed = JSON.parse(productService.custom_fields[field.key]);
            hotelIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            hotelIds = [productService.custom_fields[field.key]];
          }
        }

        hotelIds.forEach(hotelId => {
          const hotel = hotels.find(h => h.id === hotelId);
          if (hotel) {
            // Replace ID with name in the product service name
            resolvedName = resolvedName.replace(hotelId, hotel.name);
          }
        });
      } catch (error) {
        console.warn("Error resolving hotel names in product service name:", error);
      }
    }
  });

  // Resolve destination field names
  destinationFields.forEach((field: any) => {
    if (productService.custom_fields[field.key]) {
      try {
        let destinationIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(productService.custom_fields[field.key])) {
          destinationIds = productService.custom_fields[field.key];
        } else if (typeof productService.custom_fields[field.key] === 'string') {
          try {
            const parsed = JSON.parse(productService.custom_fields[field.key]);
            destinationIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            destinationIds = [productService.custom_fields[field.key]];
          }
        }

        destinationIds.forEach(destinationId => {
          const destination = destinations.find(d => d.id === destinationId);
          if (destination) {
            // Replace ID with name in the product service name
            resolvedName = resolvedName.replace(destinationId, destination.name);
          }
        });
      } catch (error) {
        console.warn("Error resolving destination names in product service name:", error);
      }
    }
  });

  return resolvedName;
};

const SupplierOfferingsPage = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  // State management - initialize from URL parameters
  const [searchTerm, setSearchTerm] = useState(
    () => searchParams.get("search") || ""
  );
  const [currentPage, setCurrentPage] = useState(() => {
    const page = searchParams.get("page");
    return page ? parseInt(page, 10) : 1;
  });
  const [pageSize, setPageSize] = useState(() => {
    const size = searchParams.get("pageSize");
    return size ? parseInt(size, 10) : 25;
  });
  const [sortField, setSortField] = useState<SortField>("updated_at");
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedOfferings, setSelectedOfferings] = useState<string[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [offeringToDelete, setOfferingToDelete] = useState<string | null>(null);

  const [filters, setFilters] = useState<FilterState>(() => ({
    supplier_id: searchParams.get("supplier_id") || "",
    category_id: searchParams.get("category_id") || "",
    product_service_id: searchParams.get("product_service_id") || "",
    status: (searchParams.get("status") as "active" | "inactive") || "",
    active_from: null,
    active_to: null,
  }));

  // Import/Export state
  const [showImportModal, setShowImportModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);

  const offset = (currentPage - 1) * pageSize;

  // API calls
  const {
    data: offeringsData,
    isLoading,
    error,
    refetch,
  } = useSupplierOfferings({
    supplier_id: filters.supplier_id || undefined,
    category_id: filters.category_id || undefined,
    product_service_id: filters.product_service_id || undefined,
    status: filters.status || undefined,
    active_from: filters.active_from
      ? filters.active_from.toISOString().split("T")[0]
      : undefined,
    active_to: filters.active_to
      ? filters.active_to.toISOString().split("T")[0]
      : undefined,
    search: searchTerm || undefined,
    limit: pageSize,
    offset,
    sort_by: sortField,
    sort_order: sortOrder,
  });

  const { data: categoriesData } = useCategories();
  const { data: suppliersData } = useSuppliers();
  const { data: productsServicesData } = useProductsServices();
  const { data: hotelsData } = useHotels();
  const { data: destinationsData } = useDestinations();

  const deleteOfferingMutation = useDeleteSupplierOffering();
  const duplicateOfferingMutation = useDuplicateSupplierOffering();
  const updateOfferingMutation = useUpdateSupplierOffering();

  // Track loading state per offering ID and field
  const [loadingCells, setLoadingCells] = useState<Set<string>>(new Set());

  // Import/Export hook
  const { generateTemplate, exportToExcel } = useSupplierOfferingImportExport();

  // Use data directly from API (no client-side filtering/sorting)
  const offerings = offeringsData?.supplier_offerings || [];
  const hotels = hotelsData?.hotels || [];
  const destinations = destinationsData?.destinations || [];

  const totalCount = offeringsData?.count || 0;

  // Debug: Log the first offering to see what data we're getting
  if (offerings.length > 0) {
    console.log("🔍 Listing Debug - First offering data:", offerings[0]);
    console.log("🔍 Listing Debug - Pricing fields:", {
      gross_price: offerings[0].gross_price,
      commission: offerings[0].commission,
      net_cost: offerings[0].net_cost,
      margin_rate: offerings[0].margin_rate,
      selling_price: offerings[0].selling_price,
      exchange_rate: offerings[0].exchange_rate,
      selling_price_selling_currency: offerings[0].selling_price_selling_currency,
    });
  }
  const totalPages = Math.ceil(totalCount / pageSize);

  // Calculate display indices for pagination info (matching suppliers page)
  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, totalCount);

  // URL synchronization effect
  useEffect(() => {
    const params = new URLSearchParams();

    if (searchTerm) params.set("search", searchTerm);
    if (filters.supplier_id) params.set("supplier_id", filters.supplier_id);
    if (filters.category_id) params.set("category_id", filters.category_id);
    if (filters.product_service_id)
      params.set("product_service_id", filters.product_service_id);
    if (filters.status) params.set("status", filters.status);
    if (currentPage > 1) params.set("page", currentPage.toString());
    if (pageSize !== 25) params.set("pageSize", pageSize.toString());

    setSearchParams(params);
  }, [searchTerm, filters, currentPage, pageSize, setSearchParams]);

  // Event handlers
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("asc");
    }
  };

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value === "all" ? "" : value }));
    setCurrentPage(1);
  };

  const handleDateFilterChange = (
    key: "active_from" | "active_to",
    value: Date | null
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setFilters({
      supplier_id: "",
      category_id: "",
      product_service_id: "",
      status: "",
      active_from: null,
      active_to: null,
    });
    setSearchTerm("");
    setCurrentPage(1);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Helper function to create cell key
  const getCellKey = (offeringId: string, field: string) =>
    `${offeringId}-${field}`;

  // Inline edit handlers
  const handleUpdateOffering = async (
    offeringId: string,
    updateData: any,
    field: string
  ) => {
    const cellKey = getCellKey(offeringId, field);

    // Set loading state for this specific cell
    setLoadingCells((prev) => new Set(prev).add(cellKey));

    try {
      await updateOfferingMutation.mutateAsync({
        id: offeringId,
        data: updateData,
      });
      // Show success message only if no error occurred
      toast.success("Supplier offering updated successfully");
    } catch (error: any) {
      console.error("Error updating supplier offering:", error);

      // Handle specific error types
      if (
        error?.response?.status === 409 ||
        error?.message?.includes("Date range conflict") ||
        error?.message?.includes("already exists") ||
        error?.message?.includes("same configuration")
      ) {
        // Extract the detailed error message if available
        let errorMessage =
          "This supplier offering configuration already exists.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        toast.error(errorMessage);
      } else if (error?.response?.status === 400) {
        // Handle validation errors
        let errorMessage = "Invalid data provided.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        toast.error(errorMessage);
      } else {
        // Generic error handling
        toast.error(error?.message || "Failed to update supplier offering");
      }

      // Re-throw the error so the component can handle it appropriately
      throw error;
    } finally {
      // Clear loading state for this cell
      setLoadingCells((prev) => {
        const newSet = new Set(prev);
        newSet.delete(cellKey);
        return newSet;
      });
    }
  };

  const handleUpdateCost = async (offeringId: string, cost: number | null) => {
    await handleUpdateOffering(offeringId, { cost }, "cost");
  };

  const handleUpdateStatus = async (offeringId: string, status: string) => {
    await handleUpdateOffering(offeringId, { status }, "status");
  };

  const handleUpdateValidityPeriod = async (
    offeringId: string,
    data: { active_from?: string | null; active_to?: string | null }
  ) => {
    await handleUpdateOffering(offeringId, data, "validity");
  };

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      default:
        return "grey";
    }
  };

  const handleSelectOffering = (offeringId: string) => {
    setSelectedOfferings((prev) =>
      prev.includes(offeringId)
        ? prev.filter((id) => id !== offeringId)
        : [...prev, offeringId]
    );
  };

  const handleSelectAll = () => {
    if (selectedOfferings.length === offerings.length) {
      setSelectedOfferings([]);
    } else {
      setSelectedOfferings(offerings.map((offering) => offering.id));
    }
  };

  const handleDelete = (id: string) => {
    setOfferingToDelete(id);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (offeringToDelete) {
      await deleteOfferingMutation.mutateAsync(offeringToDelete);
      setShowDeleteModal(false);
      setOfferingToDelete(null);
    }
  };

  const getStatusBatge = (status: string) => {
    const statusConfig = {
      active: { color: "green" as const, label: "Active" },
      inactive: { color: "red" as const, label: "Inactive" },
      pending: { color: "orange" as const, label: "Pending" },
    };

    const statusInfo = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge color={statusInfo.color}>{statusInfo.label}</Badge>;
  };

  const handleDuplicate = async (id: string) => {
    await duplicateOfferingMutation.mutateAsync(id);
  };

  const formatValidityPeriod = (activeFrom?: string, activeTo?: string) => {
    if (!activeFrom && !activeTo) return "No validity period";

    const formatDate = (dateStr: string) => {
      const date = new Date(dateStr);
      // Use consistent DD/MM/YYYY format to match Excel import expectations
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    };

    if (activeFrom && activeTo) {
      return `${formatDate(activeFrom)} → ${formatDate(activeTo)}`;
    } else if (activeFrom) {
      // Use consistent format with inline edit cell
      return `${formatDate(activeFrom)} → Open`;
    } else if (activeTo) {
      return `Until ${formatDate(activeTo)}`;
    }

    return "No validity period";
  };

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4 text-ui-fg-muted" />;
    }
    return sortOrder === "asc" ? (
      <ChevronUp className="h-4 w-4 text-ui-fg-base" />
    ) : (
      <ChevronDown className="h-4 w-4 text-ui-fg-base" />
    );
  };

  if (error) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text className="text-ui-fg-error">
            Error loading supplier offerings: {error.message}
          </Text>
        </div>
      </Container>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Supplier Offerings</Heading>
            <Text className="text-ui-fg-subtle">
              Manage which suppliers offer which products and services
            </Text>
          </div>
          <div className="flex items-center gap-x-2">
            <Button
              variant="secondary"
              onClick={() => setShowExportModal(true)}
            >
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button
              variant="secondary"
              onClick={() => setShowImportModal(true)}
            >
              <Upload className="h-4 w-4" />
              Import
            </Button>
            <Button
              onClick={() =>
                navigate("/supplier-management/supplier-offerings/create")
              }
            >
              <PlusMini />
              Add Offering
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="px-6 py-4 space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Input
                placeholder="Search by supplier name, product/service, or category..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
            <Button
              variant="secondary"
              size="small"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4" />
              Filters
              {(filters.supplier_id ||
                filters.category_id ||
                filters.product_service_id ||
                filters.status ||
                filters.active_from ||
                filters.active_to) && (
                  <Badge variant="blue" size="small" className="ml-2">
                    {
                      [
                        filters.supplier_id,
                        filters.category_id,
                        filters.product_service_id,
                        filters.status,
                        filters.active_from,
                        filters.active_to,
                      ].filter((v) => v).length
                    }
                  </Badge>
                )}
            </Button>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-ui-bg-subtle rounded-lg">
              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Supplier
                </Text>
                <Select
                  value={filters.supplier_id || "all"}
                  onValueChange={(value) =>
                    handleFilterChange("supplier_id", value)
                  }
                >
                  <Select.Trigger>
                    <Select.Value placeholder="All suppliers" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All suppliers</Select.Item>
                    {suppliersData?.suppliers?.map((supplier: any) => (
                      <Select.Item key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </div>

              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Category
                </Text>
                <Select
                  value={filters.category_id || "all"}
                  onValueChange={(value) =>
                    handleFilterChange("category_id", value)
                  }
                >
                  <Select.Trigger>
                    <Select.Value placeholder="All categories" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All categories</Select.Item>
                    {categoriesData?.categories?.map((category: any) => (
                      <Select.Item key={category.id} value={category.id}>
                        {category.name}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </div>

              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Product/Service
                </Text>
                <Select
                  value={filters.product_service_id || "all"}
                  onValueChange={(value) =>
                    handleFilterChange("product_service_id", value)
                  }
                >
                  <Select.Trigger>
                    <Select.Value placeholder="All products/services" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All products/services</Select.Item>
                    {productsServicesData?.product_services?.map((ps: any) => (
                      <Select.Item key={ps.id} value={ps.id}>
                        {ps.name}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </div>

              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Status
                </Text>
                <Select
                  value={filters.status || "all"}
                  onValueChange={(value) => handleFilterChange("status", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="All statuses" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All statuses</Select.Item>
                    <Select.Item value="active">Active</Select.Item>
                    <Select.Item value="inactive">Inactive</Select.Item>
                  </Select.Content>
                </Select>
              </div>

              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Valid From
                </Text>
                <DatePicker
                  value={filters.active_from}
                  onChange={(date) =>
                    handleDateFilterChange("active_from", date)
                  }
                  placeholder="Select start date"
                />
              </div>

              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Valid To
                </Text>
                <DatePicker
                  value={filters.active_to}
                  onChange={(date) => handleDateFilterChange("active_to", date)}
                  placeholder="Select end date"
                />
              </div>

              <div className="md:col-span-3 flex justify-end">
                <Button variant="secondary" size="small" onClick={clearFilters}>
                  <X className="h-4 w-4" />
                  Clear Filters
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>
                <div className="flex items-center gap-2">
                  Supplier Name
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell
                className="cursor-pointer"
                onClick={() => handleSort("product_service_name")}
              >
                <div className="flex items-center gap-2">
                  Product/Service Name
                  <SortIcon field="product_service_name" />
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell>
                <div className="flex items-center gap-2">
                  Category
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell>
                <div className="flex items-center gap-2">
                  Commission (%)
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell>
                <div className="flex items-center gap-2">
                  Gross Price
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell
                className="cursor-pointer"
                onClick={() => handleSort("net_cost")}
              >
                <div className="flex items-center gap-2">
                  Net Cost
                  <SortIcon field="net_cost" />
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell>
                <div className="flex items-center gap-2">
                  Margin (%)
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell>
                <div className="flex items-center gap-2">
                  Selling Price (Cost Currency)
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell>
                <div className="flex items-center gap-2">
                  Exchange Rate
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell>
                <div className="flex items-center gap-2">
                  Selling Price (Selling currency)
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell>
                <div className="flex items-center gap-2">
                  Status
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell
                className="cursor-pointer"
                onClick={() => handleSort("validity")}
              >
                <div className="flex items-center gap-2">
                  Validity
                  <SortIcon field="validity" />
                </div>
              </Table.HeaderCell>
              <Table.HeaderCell>Actions</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {isLoading ? (
              <Table.Row>
                <Table.Cell colSpan={15} className="text-center py-8">
                  Loading supplier offerings...
                </Table.Cell>
              </Table.Row>
            ) : offerings.length === 0 ? (
              <Table.Row>
                <Table.Cell colSpan={15} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <Package className="h-8 w-8 text-ui-fg-muted" />
                    <Text>No supplier offerings found</Text>
                    <Text size="small" className="text-ui-fg-subtle">
                      {searchTerm ||
                        filters.supplier_id ||
                        filters.category_id ||
                        filters.product_service_id ||
                        filters.status ||
                        filters.active_from ||
                        filters.active_to
                        ? "Try adjusting your search or filters"
                        : "Get started by creating your first supplier offering"}
                    </Text>
                  </div>
                </Table.Cell>
              </Table.Row>
            ) : (
              offerings.map((offering : any) => (
                <Table.Row key={offering.id}>
                  <Table.Cell>
                    <div>
                      <div className="font-medium">
                        {offering.supplier?.name || "Unknown Supplier"}
                      </div>
                      <div className="text-sm text-ui-fg-subtle">
                        {offering.supplier?.type}
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div>
                      <div className="font-medium line-clamp-3">
                        {resolveProductServiceName(offering.product_service, hotels, destinations)}
                      </div>
                      <div className="text-sm text-ui-fg-subtle">
                        {offering.product_service?.type}
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell >
                    <Badge variant="grey" className="pt-6 pb-6">
                      {offering.product_service?.category?.name ||
                        "No Category"}
                    </Badge>
                  </Table.Cell>
                  {/* Commission (%) */}
                  <Table.Cell>
                    <div className="text-right">
                      {offering.commission ? (
                        <span className="font-medium">
                          {(parseFloat(offering.commission) * 100).toFixed(1)}%
                        </span>
                      ) : (
                        <span className="text-ui-fg-subtle">N/A</span>
                      )}
                    </div>
                  </Table.Cell>
                  {/* Gross Price */}
                  <Table.Cell>
                    <div className="text-right">
                      {offering.gross_price ? (
                        <span className="font-medium">
                          {parseFloat(offering.gross_price).toFixed(2)} {offering.currency || "CHF"}
                        </span>
                      ) : (
                        <span className="text-ui-fg-subtle">N/A</span>
                      )}
                    </div>
                  </Table.Cell>
                  {/* Net Cost */}
                  <Table.Cell>
                    <div className="text-right">
                      {(offering.net_cost || offering.net_price || offering.cost) ? (
                        <span className="font-medium">
                          {parseFloat(offering.net_cost || offering.net_price || offering.cost).toFixed(2)} {offering.currency || "CHF"}
                        </span>
                      ) : (
                        <span className="text-ui-fg-subtle">N/A</span>
                      )}
                    </div>
                  </Table.Cell>
                  {/* Margin (%) */}
                  <Table.Cell>
                    <div className="text-right">
                      {offering.margin_rate ? (
                        <span className="font-medium">
                          {(parseFloat(offering.margin_rate) * 100).toFixed(1)}%
                        </span>
                      ) : (
                        <span className="text-ui-fg-subtle">N/A</span>
                      )}
                    </div>
                  </Table.Cell>
                  {/* Selling Price (Cost Currency) */}
                  <Table.Cell>
                    <div className="text-right">
                      {offering.selling_price ? (
                        <span className="font-medium">
                          {parseFloat(offering.selling_price).toFixed(2)} {offering.currency || "CHF"}
                        </span>
                      ) : (
                        <span className="text-ui-fg-subtle">N/A</span>
                      )}
                    </div>
                  </Table.Cell>
                  {/* Exchange Rate */}
                  <Table.Cell>
                    <div className="text-right">
                      {offering.exchange_rate ? (
                        <span className="font-medium">
                          {parseFloat(offering.exchange_rate).toFixed(4)}
                        </span>
                      ) : (
                        <span className="text-ui-fg-subtle">N/A</span>
                      )}
                    </div>
                  </Table.Cell>
                  {/* Selling Price (Selling Currency) */}
                  <Table.Cell>
                    <div className="text-right">
                      {offering.selling_price_selling_currency ? (
                        <span className="font-medium">
                          {parseFloat(offering.selling_price_selling_currency).toFixed(2)} {offering.selling_currency || "EUR"}
                        </span>
                      ) : (
                        <span className="text-ui-fg-subtle">N/A</span>
                      )}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    {/* <PopoverEditStatusCell
                      value={offering.status}
                      onSave={(status) =>
                        handleUpdateStatus(offering.id, status)
                      }
                      isLoading={loadingCells.has(
                        getCellKey(offering.id, "status")
                      )}
                    /> */}
                    {getStatusBatge(offering.status)}
                  </Table.Cell>
                  <Table.Cell>
                    {/* Debug: Log validity values being passed to component */}
                    {console.log("🔍 Listing Debug - Validity values for offering:", offering.id, {
                      active_from: offering.active_from,
                      active_to: offering.active_to,
                      active_from_type: typeof offering.active_from,
                      active_to_type: typeof offering.active_to
                    })}
                    <PopoverEditValidityCell
                      activeFrom={offering.active_from}
                      activeTo={offering.active_to}
                      onSave={(data) =>
                        handleUpdateValidityPeriod(offering.id, data)
                      }
                      isLoading={loadingCells.has(
                        getCellKey(offering.id, "validity")
                      )}
                    />
                  </Table.Cell>
                  <Table.Cell>
                    <DropdownMenu>
                      <DropdownMenu.Trigger asChild>
                        <Button variant="transparent" size="small">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenu.Trigger>
                      <DropdownMenu.Content align="end">
                        <DropdownMenu.Item
                          onClick={() =>
                            navigate(
                              `/supplier-management/supplier-offerings/${offering.id}`
                            )
                          }
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenu.Item>
                        {/* <DropdownMenu.Item
                          onClick={() =>
                            navigate(
                              `/supplier-management/supplier-offerings/${offering.id}/edit`
                            )
                          }
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenu.Item>
                        <DropdownMenu.Item
                          onClick={() =>
                            navigate(
                              `/supplier-management/supplier-offerings/${offering.id}#cost-history`
                            )
                          }
                        >
                          <History className="h-4 w-4 mr-2" />
                          View Cost History
                        </DropdownMenu.Item> */}
                        <DropdownMenu.Item
                          onClick={() => handleDuplicate(offering.id)}
                          disabled={duplicateOfferingMutation.isPending}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          {duplicateOfferingMutation.isPending ? "Duplicating..." : "Duplicate"}
                        </DropdownMenu.Item>
                        <DropdownMenu.Separator />
                        <DropdownMenu.Item
                          onClick={() => handleDelete(offering.id)}
                          className="text-ui-fg-error"
                        >
                          <Trash className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenu.Item>
                      </DropdownMenu.Content>
                    </DropdownMenu>
                  </Table.Cell>
                </Table.Row>
              ))
            )}
          </Table.Body>
        </Table>
        </div>

        {/* Pagination */}
        {totalCount > 0 && (
          <div className="flex flex-col sm:flex-row items-center justify-between px-6 py-4 border-t gap-4">
            <div className="flex items-center gap-4">
              <Text size="small" className="text-ui-fg-subtle">
                Showing {startIndex} to {endIndex} of {totalCount} offerings
              </Text>

              {/* Page Size Selector */}
              <div className="flex items-center gap-2">
                <Text size="small" className="text-ui-fg-subtle">
                  Show:
                </Text>
                <Select
                  value={pageSize.toString()}
                  onValueChange={(value) =>
                    handlePageSizeChange(parseInt(value))
                  }
                >
                  <Select.Trigger className="w-20">
                    <Select.Value />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="10">10</Select.Item>
                    <Select.Item value="25">25</Select.Item>
                    <Select.Item value="50">50</Select.Item>
                    <Select.Item value="100">100</Select.Item>
                  </Select.Content>
                </Select>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {(() => {
                  const maxVisiblePages = 5;
                  const startPage = Math.max(
                    1,
                    currentPage - Math.floor(maxVisiblePages / 2)
                  );
                  const endPage = Math.min(
                    totalPages,
                    startPage + maxVisiblePages - 1
                  );
                  const adjustedStartPage = Math.max(
                    1,
                    endPage - maxVisiblePages + 1
                  );

                  return Array.from(
                    { length: endPage - adjustedStartPage + 1 },
                    (_, i) => {
                      const page = adjustedStartPage + i;
                      return (
                        <Button
                          key={page}
                          variant={
                            currentPage === page ? "primary" : "secondary"
                          }
                          size="small"
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </Button>
                      );
                    }
                  );
                })()}
              </div>

              <Button
                variant="secondary"
                size="small"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </Container>

      {/* Delete Confirmation Prompt */}
      <Prompt open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier Offering</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete this supplier offering? This
              action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setShowDeleteModal(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={confirmDelete}
              disabled={deleteOfferingMutation.isPending}
            >
              {deleteOfferingMutation.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      {/* Import Modal */}
      <SupplierOfferingImportModal
        open={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImportComplete={() => {
          setShowImportModal(false);
          // Refresh the data without page reload
          refetch();
        }}
      />

      {/* Export Modal */}
      <SupplierOfferingExportModal
        open={showExportModal}
        onClose={() => setShowExportModal(false)}
      />

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Supplier Offerings",
  icon: Package,
});

export default SupplierOfferingsPage;
